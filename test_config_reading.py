#!/usr/bin/env python3
"""
Test script to verify configuration reading
"""

import os
import sys

# Add the scripts directory to the path
sys.path.append('scripts')

def test_config_reading():
    """Test if configuration is being read correctly"""
    
    print("🔍 TESTING CONFIGURATION READING")
    print("=" * 50)
    
    # Test direct file reading
    config_file = "include/wild_encounters_config.h"
    
    print(f"📁 Reading config file: {config_file}")
    
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        print(f"✅ Config file read successfully ({len(content)} characters)")
        
        # Check specific settings
        randomize_only_additional = "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content
        randomize_regular = "RANDOMIZE_REGULAR_TRAINERS TRUE" in content
        randomize_boss = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        
        print(f"\n📊 Configuration Values:")
        print(f"   RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        print(f"   RANDOMIZE_REGULAR_TRAINERS: {randomize_regular}")
        print(f"   RANDOMIZE_BOSS_TRAINERS: {randomize_boss}")
        
        # Test the function from insert.py
        try:
            from insert import ShouldRandomizeOnlyAdditionalPokemon
            function_result = ShouldRandomizeOnlyAdditionalPokemon()
            
            print(f"\n🔧 Function Test:")
            print(f"   ShouldRandomizeOnlyAdditionalPokemon(): {function_result}")
            
            if function_result == randomize_only_additional:
                print(f"   ✅ Function returns correct value")
            else:
                print(f"   ❌ Function mismatch! Expected: {randomize_only_additional}, Got: {function_result}")
                
        except ImportError as e:
            print(f"   ❌ Failed to import function: {e}")
            return False
        
        # Show relevant config lines
        print(f"\n📋 Relevant Config Lines:")
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'RANDOMIZE_ONLY_ADDITIONAL_POKEMON' in line:
                print(f"   Line {i+1}: {line.strip()}")
            elif 'RANDOMIZE_REGULAR_TRAINERS' in line:
                print(f"   Line {i+1}: {line.strip()}")
            elif 'RANDOMIZE_BOSS_TRAINERS' in line:
                print(f"   Line {i+1}: {line.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading config file: {e}")
        return False

def test_workflow_logic():
    """Test the workflow logic to understand the issue"""
    
    print(f"\n🔍 TESTING WORKFLOW LOGIC")
    print("=" * 50)
    
    try:
        from insert import (
            ShouldRandomizeOnlyAdditionalPokemon,
            RandomizeTrainerPartyWithOriginalData
        )
        
        # Test the configuration check
        randomize_only_additional = ShouldRandomizeOnlyAdditionalPokemon()
        print(f"📊 Configuration Check:")
        print(f"   RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        
        if randomize_only_additional:
            print(f"   ✅ Should call RandomizeOnlyAdditionalPokemonCorrected()")
            print(f"   ❌ Should NOT call RandomizeAllTrainerPokemon()")
        else:
            print(f"   ❌ Should call RandomizeAllTrainerPokemon()")
            print(f"   ✅ Should NOT call RandomizeOnlyAdditionalPokemonCorrected()")
        
        # Check if there are multiple randomization calls
        print(f"\n🔍 Checking for multiple randomization calls...")
        
        # This would require analyzing the compilation logs or ROM
        # For now, let's check the function signatures
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False

def analyze_compilation_workflow():
    """Analyze what might be happening during compilation"""
    
    print(f"\n🔍 ANALYZING COMPILATION WORKFLOW")
    print("=" * 50)
    
    print("Based on debug results, here's what's happening:")
    print()
    print("1. 📈 EXPANSION PHASE (Working correctly):")
    print("   - Party sizes increased (Koga: 4→6, Brock: 2→5, Misty: 2→5)")
    print("   - Logs show correct Pokemon additions with types")
    print("   - RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE is read correctly")
    print()
    print("2. 🎲 RANDOMIZATION PHASE (Problem here):")
    print("   - Original Pokemon are being replaced instead of preserved")
    print("   - This suggests RandomizeAllTrainerPokemon() is being called")
    print("   - OR RandomizeOnlyAdditionalPokemonCorrected() has a bug")
    print()
    print("3. 🔍 POSSIBLE CAUSES:")
    print("   a) Multiple randomization passes:")
    print("      - Expansion adds correct Pokemon")
    print("      - Later randomization overwrites everything")
    print("   b) Configuration mismatch:")
    print("      - Different parts of code read config differently")
    print("   c) Function logic error:")
    print("      - RandomizeOnlyAdditionalPokemonCorrected() modifies original slots")
    print("   d) ROM writing issue:")
    print("      - Writes happening to wrong offsets")
    print()
    print("4. 🎯 NEXT STEPS:")
    print("   - Verify which function is actually being called")
    print("   - Check if there are multiple randomization entry points")
    print("   - Add debug logging to see the exact flow")

if __name__ == "__main__":
    print("🔍 CONFIGURATION AND WORKFLOW ANALYSIS")
    print("=" * 60)
    
    # Test configuration reading
    config_success = test_config_reading()
    
    # Test workflow logic
    workflow_success = test_workflow_logic()
    
    # Analyze compilation workflow
    analyze_compilation_workflow()
    
    print("\n" + "=" * 60)
    print("🏁 ANALYSIS COMPLETE")
    
    if config_success and workflow_success:
        print("✅ Configuration reading works correctly")
        print("⚠️  Issue is likely in the randomization workflow logic")
    else:
        print("❌ Configuration reading has issues")
    
    sys.exit(0)
