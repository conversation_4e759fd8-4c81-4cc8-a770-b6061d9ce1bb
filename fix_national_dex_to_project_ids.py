#!/usr/bin/env python3
"""
Script para corrigir todos os locais onde National Dex IDs são usados incorretamente
em vez dos Project Species IDs corretos.
"""

import re
import os

def LoadProjectSpeciesMapping():
    """Carrega o mapeamento completo entre National Dex e Project Species IDs"""
    
    print("🔍 Carregando mapeamento Project Species -> National Dex...")
    
    # Carregar species.h para obter Project Species IDs
    species_to_project_id = {}
    try:
        with open("include/species.h", "r", encoding="utf-8") as f:
            species_content = f.read()
        
        # Encontrar todas as definições SPECIES_NAME
        species_pattern = r'#define\s+SPECIES_(\w+)\s+(\w+)'
        species_matches = re.findall(species_pattern, species_content)
        
        for species_name, id_value in species_matches:
            if id_value.startswith('0x'):
                project_id = int(id_value, 16)
            else:
                project_id = int(id_value)
            species_to_project_id[species_name] = project_id
        
        print(f"   ✅ Carregados {len(species_to_project_id)} Species IDs do projeto")
        
    except Exception as e:
        print(f"   ❌ Erro ao carregar species.h: {e}")
        return {}
    
    # Carregar Species_To_Pokdex_Table.c para obter mapeamento
    project_to_national = {}
    national_to_project = {}
    
    try:
        with open("src/Species_To_Pokdex_Table.c", "r", encoding="utf-8") as f:
            table_content = f.read()
        
        # Encontrar mapeamentos [SPECIES_NAME - 1] = NATIONAL_DEX_NAME
        mapping_pattern = r'\[SPECIES_(\w+)\s*-\s*1\]\s*=\s*NATIONAL_DEX_(\w+)'
        mapping_matches = re.findall(mapping_pattern, table_content)
        
        # Carregar pokedex.h para obter National Dex IDs
        with open("include/pokedex.h", "r", encoding="utf-8") as f:
            pokedex_content = f.read()
        
        for species_name, national_name in mapping_matches:
            # Obter Project Species ID
            project_id = species_to_project_id.get(species_name)
            if not project_id:
                continue
            
            # Obter National Dex ID
            dex_pattern = rf'#define\s+NATIONAL_DEX_{re.escape(national_name)}\s+(\d+)'
            dex_match = re.search(dex_pattern, pokedex_content)
            if dex_match:
                national_id = int(dex_match.group(1))
                project_to_national[project_id] = national_id
                national_to_project[national_id] = project_id
        
        print(f"   ✅ Carregados {len(project_to_national)} mapeamentos Project->National")
        print(f"   ✅ Carregados {len(national_to_project)} mapeamentos National->Project")
        
    except Exception as e:
        print(f"   ❌ Erro ao carregar mapeamentos: {e}")
        return {}
    
    return {
        'species_to_project_id': species_to_project_id,
        'project_to_national': project_to_national,
        'national_to_project': national_to_project
    }

def GetLegendaryPokemonProjectIDs(mapping_data):
    """Converte lista de lendários de National Dex para Project Species IDs"""
    
    print("\n🏆 Convertendo lendários de National Dex para Project Species IDs...")
    
    # Lista completa de lendários (National Dex IDs)
    legendary_national_ids = {
        # Gen I Legendaries
        144, 145, 146, 150, 151,  # Articuno, Zapdos, Moltres, Mewtwo, Mew
        
        # Gen II Legendaries  
        243, 244, 245, 249, 250, 251,  # Raikou, Entei, Suicune, Lugia, Ho-Oh, Celebi
        
        # Gen III Legendaries
        377, 378, 379, 380, 381, 382, 383, 384, 385, 386,  # Regirock, Regice, Registeel, Latias, Latios, Kyogre, Groudon, Rayquaza, Jirachi, Deoxys
        
        # Gen IV Legendaries
        480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493,  # Lake trio, Dialga, Palkia, Heatran, Regigigas, Giratina, Cresselia, Phione, Manaphy, Darkrai, Shaymin, Arceus
        
        # Gen V Legendaries
        494, 638, 639, 640, 641, 642, 644, 645, 646, 647, 648, 649,  # Victini, Cobalion, Terrakion, Virizion, Tornadus, Thundurus, Reshiram, Zekrom, Landorus, Kyurem, Keldeo, Meloetta, Genesect
        
        # Gen VI Legendaries
        716, 717, 718, 719, 720, 721,  # Xerneas, Yveltal, Zygarde, Diancie, Hoopa, Volcanion
        
        # Gen VII Legendaries
        785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 807, 808, 809,  # Tapu quartet, Cosmog line, Necrozma, Magearna, Marshadow, Zeraora
        
        # Gen VIII Legendaries
        888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898,  # Zacian, Zamazenta, Eternatus, Kubfu, Urshifu, Regieleki, Regidrago, Glastrier, Spectrier, Calyrex
        
        # Gen IX Legendaries
        1007, 1008, 1009, 1010,  # Koraidon, Miraidon, Walking Wake, Iron Leaves
    }
    
    national_to_project = mapping_data['national_to_project']
    legendary_project_ids = set()
    
    print("   Convertendo lendários conhecidos:")
    for national_id in sorted(legendary_national_ids):
        project_id = national_to_project.get(national_id)
        if project_id:
            legendary_project_ids.add(project_id)
            print(f"     National #{national_id} -> Project #{project_id}")
        else:
            print(f"     National #{national_id} -> ❌ Não encontrado no projeto")
    
    # Adicionar lendários específicos do projeto expandido (Ultra Beasts, Paradox, etc.)
    # Estes são identificados manualmente baseados no Base_Stats.c
    expanded_legendaries = {
        # Ultra Beasts (Gen VII)
        1010,  # Nihilego (Rock/Poison)
        1075,  # Naganadel (Poison/Dragon)
        
        # Eternatus forms (Gen VIII)
        1182,  # Eternatus (Poison/Dragon)
        1207,  # Eternatus-Eternamax (Poison/Dragon)
        
        # Paradox Pokemon (Gen IX) - considerados lendários no projeto
        1391,  # Iron Moth (Fire/Poison)
        1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400,  # Outros Paradox
    }
    
    print("   Adicionando lendários específicos do projeto expandido:")
    for project_id in sorted(expanded_legendaries):
        legendary_project_ids.add(project_id)
        print(f"     Project #{project_id} (expandido)")
    
    print(f"\n   ✅ Total de lendários (Project IDs): {len(legendary_project_ids)}")
    return legendary_project_ids

def FixLegendaryListInInsertPy(legendary_project_ids):
    """Corrige a lista de lendários em scripts/insert.py"""
    
    print("\n🔧 Corrigindo lista de lendários em scripts/insert.py...")
    
    try:
        with open("scripts/insert.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Encontrar a função GetLegendaryPokemonWithType
        function_start = content.find("def GetLegendaryPokemonWithType(")
        if function_start == -1:
            print("   ❌ Função GetLegendaryPokemonWithType não encontrada")
            return False
        
        # Encontrar o início da lista legendary_pokemon
        list_start = content.find("legendary_pokemon = {", function_start)
        if list_start == -1:
            print("   ❌ Lista legendary_pokemon não encontrada")
            return False
        
        # Encontrar o fim da lista
        brace_count = 0
        list_end = list_start
        for i, char in enumerate(content[list_start:], list_start):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    list_end = i + 1
                    break
        
        if list_end == list_start:
            print("   ❌ Fim da lista legendary_pokemon não encontrado")
            return False
        
        # Gerar nova lista com Project Species IDs corretos
        new_list = "legendary_pokemon = {\n"
        new_list += "        # CORRECTED: Using Project Species IDs instead of National Dex IDs\n"
        
        # Organizar por gerações (aproximadamente)
        gen_ranges = [
            (1, 151, "Gen I-II Legendaries"),
            (152, 386, "Gen III Legendaries"), 
            (387, 493, "Gen IV Legendaries"),
            (494, 649, "Gen V Legendaries"),
            (650, 721, "Gen VI Legendaries"),
            (722, 809, "Gen VII Legendaries"),
            (810, 898, "Gen VIII Legendaries"),
            (899, 1500, "Gen IX+ Legendaries (including Paradox)")
        ]
        
        for min_id, max_id, gen_name in gen_ranges:
            gen_legendaries = [pid for pid in sorted(legendary_project_ids) if min_id <= pid <= max_id]
            if gen_legendaries:
                new_list += f"        # {gen_name}\n"
                # Quebrar em linhas de ~10 IDs
                for i in range(0, len(gen_legendaries), 10):
                    chunk = gen_legendaries[i:i+10]
                    new_list += f"        {', '.join(map(str, chunk))},\n"
                new_list += "\n"
        
        new_list += "    }"
        
        # Substituir a lista antiga pela nova
        new_content = content[:list_start] + new_list + content[list_end:]
        
        # Salvar o arquivo corrigido
        with open("scripts/insert.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print(f"   ✅ Lista de lendários corrigida com {len(legendary_project_ids)} Project Species IDs")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao corrigir insert.py: {e}")
        return False

def FixOtherLegendaryLists(legendary_project_ids):
    """Corrige outras listas de lendários no projeto"""
    
    print("\n🔧 Corrigindo outras listas de lendários...")
    
    files_to_fix = [
        "extract_gym_leaders_parties.py",
        "identify_pokemon_1007.py",
        "debug_id_mapping.py"
    ]
    
    for filename in files_to_fix:
        if not os.path.exists(filename):
            continue
            
        try:
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Procurar por listas de lendários (padrões comuns)
            patterns = [
                r'legendary_ids\s*=\s*\{[^}]+\}',
                r'known_legendaries\s*=\s*\{[^}]+\}',
                r'legendary_pokemon\s*=\s*\{[^}]+\}'
            ]
            
            modified = False
            for pattern in patterns:
                if re.search(pattern, content, re.DOTALL):
                    print(f"   🔍 Encontrada lista de lendários em {filename}")
                    # Para simplicidade, apenas reportar - correção manual pode ser necessária
                    modified = True
            
            if modified:
                print(f"   ⚠️  {filename} contém listas de lendários que podem precisar de correção manual")
        
        except Exception as e:
            print(f"   ❌ Erro ao verificar {filename}: {e}")

def main():
    """Função principal"""
    
    print("🚀 CORREÇÃO: National Dex IDs -> Project Species IDs")
    print("=" * 80)
    
    # Carregar mapeamentos
    mapping_data = LoadProjectSpeciesMapping()
    if not mapping_data:
        print("❌ Falha ao carregar mapeamentos")
        return
    
    # Converter lendários para Project IDs
    legendary_project_ids = GetLegendaryPokemonProjectIDs(mapping_data)
    
    # Corrigir lista principal em insert.py
    if FixLegendaryListInInsertPy(legendary_project_ids):
        print("✅ Lista principal de lendários corrigida")
    else:
        print("❌ Falha ao corrigir lista principal")
        return
    
    # Verificar outros arquivos
    FixOtherLegendaryLists(legendary_project_ids)
    
    print("\n🎯 RESUMO:")
    print("=" * 40)
    print(f"✅ {len(legendary_project_ids)} lendários convertidos para Project Species IDs")
    print("✅ Lista principal em scripts/insert.py corrigida")
    print("⚠️  Outros arquivos podem precisar de correção manual")
    print("\n🔥 PRONTO PARA TESTE!")

if __name__ == "__main__":
    main()
