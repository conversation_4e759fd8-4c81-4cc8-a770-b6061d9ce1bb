# Define as pastas e arquivos
$pastaOrigem = "E:\Desktop\StarLyraFireRed\Dynamic-Pokemon-Expansion-Gen-9-master"
$pastaDestino = "E:\Desktop\StarLyraFireRed\CFRU-expansion-Experiments"
$pastaFinal = "J:\Emulators\BizHawk\GBA"
$arquivoOriginal = "test.gba"
$arquivoRenomeado1 = "BPRE0.gba"
$arquivoBackup = "BPRE0.bak"
$arquivoFinal = "[SL] Pokémon FireRed National.gba"
$timeoutSegundos = 60  # Tempo máximo para esperar pelo arquivo (60 segundos)
$intervaloVerificacao = 5 # Intervalo entre verificações (5 segundos)

# Variáveis para rastrear o estado das operações
$arquivoOriginalMovido = $false
$arquivoBPRE0RenomeadoParaBak = $false
$arquivoTestRenomeadoParaBPRE0 = $false
$pythonExecutado = $false
$arquivoTestRenomeadoParaFinal = $false

try {
    # --- Mover e Renomear o arquivo inicial ---

    # 1. Mover o arquivo da pasta de origem para a pasta de destino
    try {
        Move-Item -Path "$pastaOrigem\$arquivoOriginal" -Destination $pastaDestino -Force
        Write-Host "Arquivo '$arquivoOriginal' movido de '$pastaOrigem' para '$pastaDestino'."
        $arquivoOriginalMovido = $true
    }
    catch {
        Write-Error "Erro ao mover o arquivo: $($_.Exception.Message)"
        throw  # Relança a exceção para o bloco 'catch' externo
    }

    # 2. Renomear o arquivo BPRE0.gba para BPRE0.bak na pasta de destino (SE EXISTIR)
    $arquivoExistente = "$pastaDestino\$arquivoRenomeado1"
    $arquivoBackupExistente = "$pastaDestino\$arquivoBackup"

    if (Test-Path $arquivoExistente) {
        # Se BPRE0.bak já existe, apague-o
        if (Test-Path $arquivoBackupExistente) {
            try {
                Remove-Item -Path $arquivoBackupExistente -Force
                Write-Host "Arquivo '$arquivoBackup' existente foi removido."
            }
            catch {
                Write-Error "Erro ao remover o arquivo '$arquivoBackup' existente: $($_.Exception.Message)"
                throw #Relança a exceção
            }
        }

        try {
            Rename-Item -Path $arquivoExistente -NewName $arquivoBackup
            Write-Host "Arquivo '$arquivoRenomeado1' renomeado para '$arquivoBackup' em '$pastaDestino'."
            $arquivoBPRE0RenomeadoParaBak = $true
        }
        catch {
            Write-Error "Erro ao renomear o arquivo existente: $($_.Exception.Message)"
            throw  # Relança a exceção para o bloco 'catch' externo
        }
    } else {
        Write-Host "Arquivo '$arquivoRenomeado1' não encontrado em '$pastaDestino'. Pulando renomeação."
    }


    # 3. Renomear o arquivo movido (test.gba) para BPRE0.gba na pasta de destino
    #Verifica se BPRE0.gba já existe
    if (Test-Path $pastaDestino\$arquivoRenomeado1){
        Remove-Item -Path "$pastaDestino\$arquivoRenomeado1" -Force
        Write-Host "Arquivo '$arquivoRenomeado1' existente foi removido."
    }
    try {
        Rename-Item -Path "$pastaDestino\$arquivoOriginal" -NewName $arquivoRenomeado1
        Write-Host "Arquivo '$arquivoOriginal' renomeado para '$arquivoRenomeado1' em '$pastaDestino'."
        $arquivoTestRenomeadoParaBPRE0 = $true
    }
    catch {
        Write-Error "Erro ao renomear o arquivo: $($_.Exception.Message)"
        throw  # Relança a exceção para o bloco 'catch' externo
    }


    # --- Executar o script Python ---

    # 4. Executar o script Python na pasta de destino
    try {
        Write-Host "Executando o script Python 'scripts/make.py' em '$pastaDestino'..."
        Push-Location $pastaDestino  # Muda para o diretório de destino
        python scripts/make.py | Write-Host  # Executa o script e mostra a saída
        Pop-Location #retorna a pasta anterior
        Write-Host "Script Python executado com sucesso."
        $pythonExecutado = $true
    }
    catch {
        Write-Error "Erro ao executar o script Python: $($_.Exception.Message)"
        Write-Warning "Pode haver um problema com o devkitPro (DLL não encontrada).  Verifique a instalação do devkitPro e as variáveis de ambiente."
        throw  # Relança a exceção para o bloco 'catch' externo
    }


    # --- Esperar pelo arquivo test.gba ---
    Write-Host "Aguardando a criação do arquivo '$arquivoOriginal'..."
    $tempoEsperado = 0
    while (!(Test-Path "$pastaDestino\$arquivoOriginal")) {
        Start-Sleep -Seconds $intervaloVerificacao
        $tempoEsperado += $intervaloVerificacao
        Write-Host "Aguardando... ($tempoEsperado segundos)"

        if ($tempoEsperado -ge $timeoutSegundos) {
            Write-Error "Timeout: O arquivo '$arquivoOriginal' não foi criado em $timeoutSegundos segundos."
            throw "Timeout ao aguardar o arquivo '$arquivoOriginal'." #Lança uma exceção
        }
    }
    Write-Host "Arquivo '$arquivoOriginal' detectado."

    # --- Renomear e Mover o arquivo final ---

    # 5. Renomear o arquivo test.gba gerado para [SL] Pokémon FireRed National.gba na pasta de destino
    try {
        Rename-Item -Path "$pastaDestino\$arquivoOriginal" -NewName $arquivoFinal
        Write-Host "Arquivo '$arquivoOriginal' renomeado para '$arquivoFinal' em '$pastaDestino'."
        $arquivoTestRenomeadoParaFinal = $true
    }
    catch {
        Write-Error "Erro ao renomear o arquivo final: $($_.Exception.Message)"
        throw  # Relança a exceção para o bloco 'catch' externo
    }

    # 6. Mover o arquivo renomeado para a pasta final, substituindo o arquivo existente
    try {
        Move-Item -Path "$pastaDestino\$arquivoFinal" -Destination $pastaFinal -Force
        Write-Host "Arquivo '$arquivoFinal' movido de '$pastaDestino' para '$pastaFinal' e substituído."
    }
    catch {
        Write-Error "Erro ao mover o arquivo final: $($_.Exception.Message)"
        throw  # Relança a exceção para o bloco 'catch' externo
    }

    Write-Host "Script concluído com sucesso!"

}
catch {
    Write-Error "Ocorreu um erro durante a execução do script: $($_.Exception.Message)"

    # --- Lógica de Limpeza e Reversão ---
    Write-Host "Iniciando procedimento de limpeza e reversão..."

    #Reverte a movimentação do arquivo original
    if ($arquivoOriginalMovido){
        try{
            Move-Item -Path "$pastaDestino\$arquivoOriginal" -Destination $pastaOrigem -Force
            Write-Host "Arquivo '$arquivoOriginal' retornado para '$pastaOrigem'."
        }
        catch{
            Write-Error "Falha ao retornar '$arquivoOriginal' para '$pastaOrigem': $($_.Exception.Message)"
        }
    }

     #Reverte a renomeação de BPRE0.gba para BPRE0.bak
    if ($arquivoBPRE0RenomeadoParaBak){
        try{
            Rename-Item -Path "$pastaDestino\$arquivoBackup" -NewName $arquivoRenomeado1
            Write-Host "Arquivo '$arquivoBackup' renomeado de volta para '$arquivoRenomeado1'."
        }
        catch{
            Write-Error "Falha ao renomear '$arquivoBackup' de volta para '$arquivoRenomeado1': $($_.Exception.Message)"
        }
    }

    #Reverte a renomeação de test.gba para BPRE0.gba
    if ($arquivoTestRenomeadoParaBPRE0){
        try{
            Rename-Item -Path "$pastaDestino\$arquivoRenomeado1" -NewName $arquivoOriginal
             Write-Host "Arquivo '$arquivoRenomeado1' renomeado de volta para '$arquivoOriginal'."
        }
        catch{
             Write-Error "Falha ao renomear '$arquivoRenomeado1' de volta para '$arquivoOriginal': $($_.Exception.Message)"
        }
    }

    #Remove o arquivo final se ele tiver sido criado e renomeado
    if ($arquivoTestRenomeadoParaFinal){
        try{
            Remove-Item -Path "$pastaDestino\$arquivoFinal" -Force
             Write-Host "Arquivo '$arquivoFinal' removido."
        }
        catch{
             Write-Error "Falha ao remover '$arquivoFinal': $($_.Exception.Message)"
        }

    }
    Write-Host "Procedimento de limpeza e reversão concluído."
}
finally {
    Write-Host "Script finalizado."
}
