#!/usr/bin/env python3
"""
Test script to verify that trainer randomization works correctly with the structure detection fix
"""

import os
import sys
import struct
import shutil

# Add the scripts directory to the path
sys.path.append('scripts')

def test_trainer_randomization_with_structure_fix():
    """Test trainer randomization with the corrected structure detection"""
    
    print("🧪 TESTING TRAINER RANDOMIZATION WITH STRUCTURE FIX")
    print("=" * 70)
    
    # Check if original ROM exists
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    # Create a backup copy for testing
    test_rom = "test_randomization.gba"
    if os.path.exists(test_rom):
        os.remove(test_rom)
    
    shutil.copy2("BPRE0.gba", test_rom)
    print(f"✅ Created test ROM: {test_rom}")
    
    try:
        from insert import (
            FindTrainerTable, 
            ReadOriginalTrainerPartyData,
            RandomizeTrainerPartyWithOriginalData,
            GetPokemonEntrySize
        )
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    try:
        # Test specific trainers with known structures
        test_cases = [
            {
                'id': 0x1A2,  # Koga (16-byte structure)
                'name': 'KOGA',
                'expected_structure': 16,
                'expected_party': [
                    {'species': 109, 'level': 37},  # Koffing
                    {'species': 89, 'level': 39},   # Muk
                    {'species': 109, 'level': 37},  # Koffing
                    {'species': 110, 'level': 43}   # Weezing
                ]
            },
            {
                'id': 0x19E,  # Brock (likely 8-byte structure)
                'name': 'BROCK',
                'expected_structure': 8,
                'expected_party': None  # Will read from ROM
            }
        ]
        
        success_count = 0
        total_tests = len(test_cases)
        
        with open("BPRE0.gba", "rb") as original_rom, open(test_rom, "r+b") as test_rom_file:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(original_rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            for test_case in test_cases:
                trainer_id = test_case['id']
                trainer_name = test_case['name']
                expected_structure = test_case['expected_structure']
                
                print(f"\n🎯 Testing {trainer_name} (ID {trainer_id:03X})")
                
                # Read trainer header
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                original_rom.seek(trainer_offset)
                trainer_data = original_rom.read(40)
                
                if len(trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                party_flags = trainer_data[0]
                trainer_class = trainer_data[1]
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                
                if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                    print(f"   ❌ Invalid party pointer: 0x{party_ptr:08X}")
                    continue
                
                party_offset = party_ptr - 0x08000000
                
                print(f"   Party flags: 0x{party_flags:02X}")
                print(f"   Party size: {party_size}")
                print(f"   Party offset: 0x{party_offset:08X}")
                
                # Test structure detection
                detected_entry_size = GetPokemonEntrySize(original_rom, party_offset, party_size, party_flags)
                print(f"   Detected entry size: {detected_entry_size} bytes")
                
                if detected_entry_size == expected_structure:
                    print(f"   ✅ Structure detection correct!")
                else:
                    print(f"   ⚠️  Structure detection mismatch: expected {expected_structure}, got {detected_entry_size}")
                
                # Read original party data
                original_party = ReadOriginalTrainerPartyData(original_rom, party_offset, party_size, party_flags)
                
                if not original_party:
                    print(f"   ❌ Failed to read original party data")
                    continue
                
                print(f"   ✅ Read {len(original_party)} Pokemon from original party")
                
                # Display original party
                for i, pokemon in enumerate(original_party):
                    structure = pokemon.get('structure', 'unknown')
                    print(f"      Slot {i+1}: Species {pokemon['species']} Lv.{pokemon['level']} ({structure})")
                
                # Test randomization (dry run - don't actually modify)
                print(f"   🔄 Testing randomization logic...")
                
                # Create a copy of the test ROM for this specific test
                test_rom_file.seek(trainer_offset)
                test_trainer_data = bytearray(test_rom_file.read(40))
                
                # Test the randomization function
                modifications = RandomizeTrainerPartyWithOriginalData(
                    original_rom, test_rom_file, party_offset, party_size, 
                    0x3203CC, trainer_class, trainer_id  # Pokemon stats offset for Fire Red
                )
                
                print(f"   📊 Randomization result: {modifications} modifications")
                
                # Read the party after randomization to verify structure is preserved
                randomized_party = ReadOriginalTrainerPartyData(test_rom_file, party_offset, party_size, party_flags)
                
                if randomized_party:
                    print(f"   ✅ Successfully read randomized party: {len(randomized_party)} Pokemon")
                    
                    # Verify all Pokemon have valid data
                    valid_pokemon = 0
                    for pokemon in randomized_party:
                        if (1 <= pokemon['level'] <= 255 and 
                            1 <= pokemon['species'] <= 1440):
                            valid_pokemon += 1
                    
                    if valid_pokemon == len(randomized_party):
                        print(f"   ✅ All randomized Pokemon have valid data")
                        success_count += 1
                    else:
                        print(f"   ⚠️  Only {valid_pokemon}/{len(randomized_party)} Pokemon have valid data")
                else:
                    print(f"   ❌ Failed to read randomized party")
        
        # Cleanup
        if os.path.exists("test_randomization.gba"):
            os.remove("test_randomization.gba")
        
        print(f"\n📊 FINAL RESULTS: {success_count}/{total_tests} tests passed")
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        
        # Cleanup on error
        if os.path.exists("test_randomization.gba"):
            os.remove("test_randomization.gba")
        
        return False

def test_structure_consistency():
    """Test that structure detection is consistent across multiple calls"""
    
    print("\n🧪 TESTING STRUCTURE DETECTION CONSISTENCY")
    print("=" * 70)
    
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    try:
        from insert import FindTrainerTable, GetPokemonEntrySize
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    try:
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            # Test multiple trainers for consistency
            test_trainer_ids = [0x19E, 0x19F, 0x1A0, 0x1A1, 0x1A2]  # Gym leaders
            
            consistent_results = 0
            total_trainers = len(test_trainer_ids)
            
            for trainer_id in test_trainer_ids:
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                rom.seek(trainer_offset)
                trainer_data = rom.read(40)
                
                if len(trainer_data) < 40:
                    continue
                
                party_flags = trainer_data[0]
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                
                if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                    continue
                
                party_offset = party_ptr - 0x08000000
                
                # Test consistency - call the function multiple times
                results = []
                for _ in range(5):
                    entry_size = GetPokemonEntrySize(rom, party_offset, party_size, party_flags)
                    results.append(entry_size)
                
                # Check if all results are the same
                if len(set(results)) == 1:
                    print(f"   ✅ Trainer {trainer_id:03X}: Consistent result ({results[0]} bytes)")
                    consistent_results += 1
                else:
                    print(f"   ❌ Trainer {trainer_id:03X}: Inconsistent results {results}")
            
            print(f"\n📊 Consistency test: {consistent_results}/{total_trainers} trainers consistent")
            return consistent_results == total_trainers
            
    except Exception as e:
        print(f"❌ Error during consistency testing: {e}")
        return False

if __name__ == "__main__":
    print("🧪 COMPREHENSIVE STRUCTURE DETECTION AND RANDOMIZATION TEST")
    print("=" * 70)
    
    # Test structure detection and randomization
    randomization_success = test_trainer_randomization_with_structure_fix()
    
    # Test consistency
    consistency_success = test_structure_consistency()
    
    print("\n" + "=" * 70)
    print("🏁 FINAL RESULTS:")
    print(f"   Randomization test: {'✅ PASS' if randomization_success else '❌ FAIL'}")
    print(f"   Consistency test: {'✅ PASS' if consistency_success else '❌ FAIL'}")
    
    if randomization_success and consistency_success:
        print("\n🎉 ALL TESTS PASSED! Structure detection and randomization are working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! There may be issues with structure detection or randomization.")
        sys.exit(1)
