#!/usr/bin/env python3
"""
Test to track exactly where Pokemon are being modified
"""

import os
import sys
import struct

# Add the scripts directory to the path
sys.path.append('scripts')

def test_pokemon_modification_tracking():
    """Track exactly where Pokemon are being modified"""
    
    print("🔍 POKEMON MODIFICATION TRACKING")
    print("=" * 60)
    
    if not os.path.exists("test.gba"):
        print("❌ Compiled ROM 'test.gba' not found")
        return False
    
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    try:
        from insert import (
            FindTrainerTable, 
            ReadOriginalTrainerPartyData,
            LoadProjectPokemonDatabase
        )
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Load project Pokemon database for type information
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        print("❌ Failed to load project Pokemon database")
        return False
    
    print(f"✅ Loaded {len(project_pokemon_data)} Pokemon from project database")
    
    # Test specific gym leaders
    test_trainers = [
        {'id': 0x1A2, 'name': 'KOGA'},
        {'id': 0x19E, 'name': 'BROCK'},
        {'id': 0x19F, 'name': 'MISTY'}
    ]
    
    try:
        with open("test.gba", "rb") as compiled_rom, open("BPRE0.gba", "rb") as original_rom:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(compiled_rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            for trainer_info in test_trainers:
                trainer_id = trainer_info['id']
                trainer_name = trainer_info['name']
                
                print(f"\n🎯 Testing {trainer_name} (ID {trainer_id:03X})")
                
                # Read trainer header from both ROMs
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                
                # Original ROM
                original_rom.seek(trainer_offset)
                original_trainer_data = original_rom.read(40)
                
                # Compiled ROM
                compiled_rom.seek(trainer_offset)
                compiled_trainer_data = compiled_rom.read(40)
                
                if len(original_trainer_data) < 40 or len(compiled_trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                # Compare trainer headers
                original_party_flags = original_trainer_data[0]
                original_party_size = original_trainer_data[32]
                original_party_ptr = struct.unpack('<I', original_trainer_data[36:40])[0]
                
                compiled_party_flags = compiled_trainer_data[0]
                compiled_party_size = compiled_trainer_data[32]
                compiled_party_ptr = struct.unpack('<I', compiled_trainer_data[36:40])[0]
                
                print(f"   📊 Party Size: {original_party_size} -> {compiled_party_size}")
                print(f"   📊 Party Flags: {original_party_flags} -> {compiled_party_flags}")
                print(f"   📊 Party Ptr: 0x{original_party_ptr:08X} -> 0x{compiled_party_ptr:08X}")
                
                if original_party_ptr < 0x08000000 or compiled_party_ptr < 0x08000000:
                    print(f"   ❌ Invalid party pointers")
                    continue
                
                original_party_offset = original_party_ptr - 0x08000000
                compiled_party_offset = compiled_party_ptr - 0x08000000
                
                # Read party data from both ROMs
                original_party = ReadOriginalTrainerPartyData(original_rom, original_party_offset, original_party_size, original_party_flags)
                compiled_party = ReadOriginalTrainerPartyData(compiled_rom, compiled_party_offset, compiled_party_size, compiled_party_flags)
                
                if not original_party or not compiled_party:
                    print(f"   ❌ Failed to read party data")
                    continue
                
                # Detailed Pokemon-by-Pokemon comparison
                print(f"   📋 Detailed Pokemon Analysis:")
                print(f"      Original party: {len(original_party)} Pokemon")
                print(f"      Compiled party: {len(compiled_party)} Pokemon")
                
                # Compare each Pokemon slot
                max_slots = max(len(original_party), len(compiled_party))
                for i in range(max_slots):
                    slot_type = "ORIGINAL" if i < len(original_party) else "ADDITIONAL"
                    
                    original_pokemon = original_party[i] if i < len(original_party) else None
                    compiled_pokemon = compiled_party[i] if i < len(compiled_party) else None
                    
                    if original_pokemon and compiled_pokemon:
                        orig_species = original_pokemon['species']
                        comp_species = compiled_pokemon['species']
                        orig_level = original_pokemon['level']
                        comp_level = compiled_pokemon['level']
                        
                        # Get Pokemon names for better readability
                        orig_name = get_pokemon_name(orig_species, project_pokemon_data)
                        comp_name = get_pokemon_name(comp_species, project_pokemon_data)
                        
                        if orig_species == comp_species:
                            status = "✅ PRESERVED"
                        else:
                            status = "❌ MODIFIED"
                        
                        print(f"      Slot {i+1} ({slot_type}): {orig_name} (#{orig_species}, Lv{orig_level}) -> {comp_name} (#{comp_species}, Lv{comp_level}) {status}")
                        
                        # Check type appropriateness for additional Pokemon
                        if slot_type == "ADDITIONAL" and orig_species != comp_species:
                            orig_types = get_pokemon_types(orig_species, project_pokemon_data)
                            comp_types = get_pokemon_types(comp_species, project_pokemon_data)
                            print(f"         Type change: {orig_types} -> {comp_types}")
                    
                    elif compiled_pokemon:
                        # Additional Pokemon only in compiled ROM
                        comp_species = compiled_pokemon['species']
                        comp_level = compiled_pokemon['level']
                        comp_name = get_pokemon_name(comp_species, project_pokemon_data)
                        comp_types = get_pokemon_types(comp_species, project_pokemon_data)
                        
                        print(f"      Slot {i+1} (ADDITIONAL): NEW -> {comp_name} (#{comp_species}, Lv{comp_level}) {comp_types}")
                
                # Summary analysis
                original_preserved = 0
                for i in range(min(len(original_party), len(compiled_party))):
                    if original_party[i]['species'] == compiled_party[i]['species']:
                        original_preserved += 1
                
                preservation_rate = (original_preserved / len(original_party) * 100) if original_party else 0
                
                print(f"   📊 Summary:")
                print(f"      Original Pokemon preserved: {original_preserved}/{len(original_party)} ({preservation_rate:.1f}%)")
                
                if preservation_rate == 100 and len(compiled_party) > len(original_party):
                    print(f"      ✅ CORRECT: Only additional Pokemon added")
                elif preservation_rate == 100:
                    print(f"      ⚠️  No changes applied")
                elif preservation_rate > 0:
                    print(f"      ⚠️  PARTIAL: Some original Pokemon preserved, some randomized")
                else:
                    print(f"      ❌ INCORRECT: All original Pokemon randomized")

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_pokemon_name(species_id, project_pokemon_data):
    """Get Pokemon name from project data"""
    if species_id in project_pokemon_data:
        return project_pokemon_data[species_id].get('name', f'Unknown#{species_id}')
    return f'Unknown#{species_id}'

def get_pokemon_types(species_id, project_pokemon_data):
    """Get Pokemon types from project data"""
    if species_id in project_pokemon_data:
        pokemon_data = project_pokemon_data[species_id]
        type1 = pokemon_data.get('type1', 0)
        type2 = pokemon_data.get('type2', 0)
        
        # Convert type numbers to names (simplified)
        type_names = {
            0: "None", 1: "Fighting", 2: "Flying", 3: "Poison", 4: "Ground",
            5: "Rock", 6: "Bug", 7: "Ghost", 8: "Steel", 9: "Fire",
            10: "Water", 11: "Grass", 12: "Electric", 13: "Psychic", 14: "Ice",
            15: "Dragon", 16: "Dark", 17: "Fairy"
        }
        
        type1_name = type_names.get(type1, f"Type{type1}")
        type2_name = type_names.get(type2, f"Type{type2}") if type2 != type1 else None
        
        if type2_name:
            return f"{type1_name}/{type2_name}"
        else:
            return type1_name
    
    return "Unknown"

if __name__ == "__main__":
    print("🔍 POKEMON MODIFICATION TRACKING")
    print("=" * 60)
    
    # Test for Pokemon modification tracking
    test_pokemon_modification_tracking()
    
    print("\n" + "=" * 60)
    print("🏁 TRACKING COMPLETE")
    
    sys.exit(0)
