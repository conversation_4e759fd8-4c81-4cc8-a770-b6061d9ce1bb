#!/usr/bin/env python3
"""
Debug script to understand what's happening with trainer randomization
"""

import os
import sys
import struct

# Add the scripts directory to the path
sys.path.append('scripts')

def debug_trainer_randomization():
    """Debug trainer randomization to understand the discrepancy"""
    
    print("🔍 DEBUGGING TRAINER RANDOMIZATION")
    print("=" * 60)
    
    if not os.path.exists("test.gba"):
        print("❌ Compiled ROM 'test.gba' not found")
        return False
    
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    try:
        from insert import (
            FindTrainerTable, 
            ReadOriginalTrainerPartyData,
            LoadProjectPokemonDatabase
        )
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Load project Pokemon database for type information
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        print("❌ Failed to load project Pokemon database")
        return False
    
    print(f"✅ Loaded {len(project_pokemon_data)} Pokemon from project database")
    
    # Test specific gym leaders
    test_trainers = [
        {'id': 0x1A2, 'name': 'KOGA'},
        {'id': 0x19E, 'name': 'BROCK'},
        {'id': 0x19F, 'name': 'MISTY'}
    ]
    
    try:
        with open("test.gba", "rb") as compiled_rom, open("BPRE0.gba", "rb") as original_rom:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(compiled_rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            for trainer_info in test_trainers:
                trainer_id = trainer_info['id']
                trainer_name = trainer_info['name']
                
                print(f"\n🎯 Debugging {trainer_name} (ID {trainer_id:03X})")
                
                # Read trainer header from both ROMs
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                
                # Original ROM
                original_rom.seek(trainer_offset)
                original_trainer_data = original_rom.read(40)
                
                # Compiled ROM
                compiled_rom.seek(trainer_offset)
                compiled_trainer_data = compiled_rom.read(40)
                
                if len(original_trainer_data) < 40 or len(compiled_trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                # Compare trainer headers
                original_party_flags = original_trainer_data[0]
                original_party_size = original_trainer_data[32]
                original_party_ptr = struct.unpack('<I', original_trainer_data[36:40])[0]
                
                compiled_party_flags = compiled_trainer_data[0]
                compiled_party_size = compiled_trainer_data[32]
                compiled_party_ptr = struct.unpack('<I', compiled_trainer_data[36:40])[0]
                
                print(f"   📊 Trainer Header Comparison:")
                print(f"      Original: flags=0x{original_party_flags:02X}, size={original_party_size}, ptr=0x{original_party_ptr:08X}")
                print(f"      Compiled: flags=0x{compiled_party_flags:02X}, size={compiled_party_size}, ptr=0x{compiled_party_ptr:08X}")
                
                if original_party_ptr < 0x08000000 or compiled_party_ptr < 0x08000000:
                    print(f"   ❌ Invalid party pointers")
                    continue
                
                original_party_offset = original_party_ptr - 0x08000000
                compiled_party_offset = compiled_party_ptr - 0x08000000
                
                print(f"      Party offsets: original=0x{original_party_offset:08X}, compiled=0x{compiled_party_offset:08X}")
                
                # Read party data from both ROMs
                print(f"   📋 Reading party data...")
                
                # Original party
                original_party = ReadOriginalTrainerPartyData(original_rom, original_party_offset, original_party_size, original_party_flags)
                if original_party:
                    print(f"   ✅ Original party: {len(original_party)} Pokemon")
                    for i, pokemon in enumerate(original_party):
                        species_name = get_pokemon_name(pokemon['species'], project_pokemon_data)
                        print(f"      Slot {i+1}: #{pokemon['species']} {species_name} Lv.{pokemon['level']}")
                else:
                    print(f"   ❌ Failed to read original party")
                
                # Compiled party
                compiled_party = ReadOriginalTrainerPartyData(compiled_rom, compiled_party_offset, compiled_party_size, compiled_party_flags)
                if compiled_party:
                    print(f"   ✅ Compiled party: {len(compiled_party)} Pokemon")
                    for i, pokemon in enumerate(compiled_party):
                        species_name = get_pokemon_name(pokemon['species'], project_pokemon_data)
                        print(f"      Slot {i+1}: #{pokemon['species']} {species_name} Lv.{pokemon['level']}")
                else:
                    print(f"   ❌ Failed to read compiled party")
                
                # Compare parties
                if original_party and compiled_party:
                    print(f"   🔍 Party Comparison:")
                    
                    if len(original_party) != len(compiled_party):
                        print(f"      ⚠️  Party size changed: {len(original_party)} -> {len(compiled_party)}")
                    
                    # Check if original Pokemon were preserved
                    original_preserved = 0
                    for i in range(min(len(original_party), len(compiled_party))):
                        if original_party[i]['species'] == compiled_party[i]['species']:
                            original_preserved += 1
                        else:
                            orig_name = get_pokemon_name(original_party[i]['species'], project_pokemon_data)
                            comp_name = get_pokemon_name(compiled_party[i]['species'], project_pokemon_data)
                            print(f"      🔄 Slot {i+1} changed: {orig_name} -> {comp_name}")
                    
                    if original_preserved == len(original_party) and len(compiled_party) > len(original_party):
                        print(f"      ✅ Original Pokemon preserved, {len(compiled_party) - len(original_party)} Pokemon added")
                    elif original_preserved == len(original_party):
                        print(f"      ✅ Original Pokemon preserved, no additions")
                    else:
                        print(f"      ⚠️  Only {original_preserved}/{len(original_party)} original Pokemon preserved")
                
                # Check configuration
                print(f"   ⚙️  Checking configuration...")
                check_randomization_config()
                
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_pokemon_name(species_id, project_pokemon_data):
    """Get Pokemon name from species ID"""
    if species_id in project_pokemon_data:
        return project_pokemon_data[species_id].get('name', f'Unknown#{species_id}')
    return f'Unknown#{species_id}'

def check_randomization_config():
    """Check the current randomization configuration"""
    config_file = "include/wild_encounters_config.h"
    
    try:
        with open(config_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check key settings
        randomize_only_additional = "RANDOMIZE_ONLY_ADDITIONAL_POKEMON TRUE" in content
        randomize_regular = "RANDOMIZE_REGULAR_TRAINERS TRUE" in content
        randomize_boss = "RANDOMIZE_BOSS_TRAINERS TRUE" in content
        
        print(f"      RANDOMIZE_ONLY_ADDITIONAL_POKEMON: {randomize_only_additional}")
        print(f"      RANDOMIZE_REGULAR_TRAINERS: {randomize_regular}")
        print(f"      RANDOMIZE_BOSS_TRAINERS: {randomize_boss}")
        
        # Check expansion settings
        import re
        regular_match = re.search(r'ADDITIONAL_REGULAR_TRAINER_POKEMON\s+(\d+)', content)
        important_match = re.search(r'ADDITIONAL_IMPORTANT_TRAINER_POKEMON\s+(\d+)', content)
        boss_match = re.search(r'ADDITIONAL_BOSS_TRAINER_POKEMON\s+(\d+)', content)
        
        regular_additional = int(regular_match.group(1)) if regular_match else 0
        important_additional = int(important_match.group(1)) if important_match else 0
        boss_additional = int(boss_match.group(1)) if boss_match else 0
        
        print(f"      ADDITIONAL_REGULAR_TRAINER_POKEMON: {regular_additional}")
        print(f"      ADDITIONAL_IMPORTANT_TRAINER_POKEMON: {important_additional}")
        print(f"      ADDITIONAL_BOSS_TRAINER_POKEMON: {boss_additional}")
        
    except Exception as e:
        print(f"      ❌ Failed to read configuration: {e}")

def analyze_compilation_logs():
    """Analyze what the compilation logs showed vs reality"""
    
    print(f"\n📊 COMPILATION LOG ANALYSIS")
    print("=" * 60)
    
    print("From compilation logs, we saw:")
    print("🏆 GIOVANNI: Added #1075")
    print("🏆 BROCK: Added #27, #332, #1392") 
    print("🏆 MISTY: Added #119, #63, #1006")
    print("🏆 LT_SURGE: Added #1033, #519, #1392")
    print("🏆 ERIKA: Added #379, #978, #1075")
    print("🏆 KOGA: Added #31, #1075")
    print("🏆 SABRINA: Added #944, #1391")
    print("🏆 BLAINE: Added #973, #1006")
    
    print("\nBut analysis shows:")
    print("❌ All gym leaders have completely different Pokemon")
    print("❌ No additional Pokemon found")
    print("❌ Original Pokemon were replaced, not preserved")
    
    print("\nPossible causes:")
    print("1. 🔄 Multiple randomization passes overwriting each other")
    print("2. ⚙️  Configuration mismatch between expansion and randomization")
    print("3. 📝 Logging happening during expansion, but final randomization overwriting")
    print("4. 🎯 Wrong ROM being analyzed (test.gba vs different output)")

if __name__ == "__main__":
    print("🔍 TRAINER RANDOMIZATION DEBUG ANALYSIS")
    print("=" * 60)
    
    debug_trainer_randomization()
    analyze_compilation_logs()
    
    print("\n" + "=" * 60)
    print("🔍 DEBUG COMPLETE: Check output above for discrepancies")
