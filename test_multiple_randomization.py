#!/usr/bin/env python3
"""
Test to detect multiple randomization passes
"""

import os
import sys
import struct

# Add the scripts directory to the path
sys.path.append('scripts')

def test_multiple_randomization():
    """Test if there are multiple randomization passes happening"""
    
    print("🔍 TESTING FOR MULTIPLE RANDOMIZATION PASSES")
    print("=" * 60)
    
    if not os.path.exists("test.gba"):
        print("❌ Compiled ROM 'test.gba' not found")
        return False
    
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    try:
        from insert import (
            FindTrainerTable, 
            ReadOriginalTrainerPartyData,
            LoadProjectPokemonDatabase
        )
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Load project Pokemon database for type information
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        print("❌ Failed to load project Pokemon database")
        return False
    
    print(f"✅ Loaded {len(project_pokemon_data)} Pokemon from project database")
    
    # Test specific gym leaders
    test_trainers = [
        {'id': 0x1A2, 'name': 'KOGA'},
        {'id': 0x19E, 'name': 'BROCK'},
        {'id': 0x19F, 'name': 'MISTY'}
    ]
    
    try:
        with open("test.gba", "rb") as compiled_rom, open("BPRE0.gba", "rb") as original_rom:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(compiled_rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            for trainer_info in test_trainers:
                trainer_id = trainer_info['id']
                trainer_name = trainer_info['name']
                
                print(f"\n🎯 Testing {trainer_name} (ID {trainer_id:03X})")
                
                # Read trainer header from both ROMs
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                
                # Original ROM
                original_rom.seek(trainer_offset)
                original_trainer_data = original_rom.read(40)
                
                # Compiled ROM
                compiled_rom.seek(trainer_offset)
                compiled_trainer_data = compiled_rom.read(40)
                
                if len(original_trainer_data) < 40 or len(compiled_trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                # Compare trainer headers
                original_party_flags = original_trainer_data[0]
                original_party_size = original_trainer_data[32]
                original_party_ptr = struct.unpack('<I', original_trainer_data[36:40])[0]
                
                compiled_party_flags = compiled_trainer_data[0]
                compiled_party_size = compiled_trainer_data[32]
                compiled_party_ptr = struct.unpack('<I', compiled_trainer_data[36:40])[0]
                
                print(f"   📊 Party Size: {original_party_size} -> {compiled_party_size}")
                
                if original_party_ptr < 0x08000000 or compiled_party_ptr < 0x08000000:
                    print(f"   ❌ Invalid party pointers")
                    continue
                
                original_party_offset = original_party_ptr - 0x08000000
                compiled_party_offset = compiled_party_ptr - 0x08000000
                
                # Read party data from both ROMs
                original_party = ReadOriginalTrainerPartyData(original_rom, original_party_offset, original_party_size, original_party_flags)
                compiled_party = ReadOriginalTrainerPartyData(compiled_rom, compiled_party_offset, compiled_party_size, compiled_party_flags)
                
                if not original_party or not compiled_party:
                    print(f"   ❌ Failed to read party data")
                    continue
                
                # Analyze what happened
                print(f"   📋 Analysis:")
                print(f"      Original party: {len(original_party)} Pokemon")
                print(f"      Compiled party: {len(compiled_party)} Pokemon")
                
                # Check if original Pokemon were preserved
                original_preserved = 0
                for i in range(min(len(original_party), len(compiled_party))):
                    if original_party[i]['species'] == compiled_party[i]['species']:
                        original_preserved += 1
                
                preservation_rate = (original_preserved / len(original_party) * 100) if original_party else 0
                
                print(f"      Original Pokemon preserved: {original_preserved}/{len(original_party)} ({preservation_rate:.1f}%)")
                
                # Determine what type of randomization happened
                if preservation_rate == 100 and len(compiled_party) > len(original_party):
                    print(f"      ✅ CORRECT: Only additional Pokemon added")
                elif preservation_rate == 100:
                    print(f"      ⚠️  No randomization applied")
                elif preservation_rate > 0:
                    print(f"      ⚠️  PARTIAL: Some original Pokemon preserved, some randomized")
                else:
                    print(f"      ❌ INCORRECT: All original Pokemon randomized")
                
                # Check if additional Pokemon are type-appropriate
                if len(compiled_party) > len(original_party):
                    additional_count = len(compiled_party) - len(original_party)
                    print(f"      Additional Pokemon: {additional_count}")
                    
                    # Analyze types of additional Pokemon
                    original_types = set()
                    for pokemon in original_party:
                        species_id = pokemon['species']
                        if species_id in project_pokemon_data:
                            pokemon_data = project_pokemon_data[species_id]
                            type1 = pokemon_data.get('type1')
                            type2 = pokemon_data.get('type2')
                            if type1 is not None:
                                original_types.add(type1)
                            if type2 is not None and type2 != type1:
                                original_types.add(type2)
                    
                    type_appropriate = 0
                    for i in range(len(original_party), len(compiled_party)):
                        pokemon = compiled_party[i]
                        species_id = pokemon['species']
                        if species_id in project_pokemon_data:
                            pokemon_data = project_pokemon_data[species_id]
                            type1 = pokemon_data.get('type1')
                            type2 = pokemon_data.get('type2')
                            
                            if type1 in original_types or type2 in original_types:
                                type_appropriate += 1
                    
                    type_rate = (type_appropriate / additional_count * 100) if additional_count > 0 else 0
                    print(f"      Type appropriate additional: {type_appropriate}/{additional_count} ({type_rate:.1f}%)")
                
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_randomization_workflow():
    """Analyze the randomization workflow to understand the issue"""
    
    print(f"\n🔍 RANDOMIZATION WORKFLOW ANALYSIS")
    print("=" * 60)
    
    print("Based on compilation logs and ROM analysis:")
    print()
    print("1. 📈 EXPANSION PHASE:")
    print("   ✅ Party sizes increased correctly")
    print("   ✅ Pokemon added with appropriate types")
    print("   ✅ Legendary substitution applied")
    print()
    print("2. 🎲 RANDOMIZATION PHASE:")
    print("   ❌ Original Pokemon being randomized")
    print("   ❌ RANDOMIZE_ONLY_ADDITIONAL_POKEMON not respected")
    print()
    print("3. 🔍 POSSIBLE CAUSES:")
    print("   a) Multiple randomization functions being called")
    print("   b) Wrong function being called (RandomizeAllTrainerPokemonWithCache)")
    print("   c) Configuration not being read correctly in some functions")
    print("   d) Function order issue (randomization after expansion)")
    print()
    print("4. 🎯 SOLUTION:")
    print("   - Ensure only RandomizeOnlyAdditionalPokemonWithCache is called")
    print("   - Verify configuration reading consistency")
    print("   - Check for multiple randomization entry points")

if __name__ == "__main__":
    print("🔍 MULTIPLE RANDOMIZATION PASS DETECTION")
    print("=" * 60)
    
    # Test for multiple randomization passes
    test_multiple_randomization()
    
    # Analyze workflow
    analyze_randomization_workflow()
    
    print("\n" + "=" * 60)
    print("🏁 TESTING COMPLETE")
    
    sys.exit(0)
