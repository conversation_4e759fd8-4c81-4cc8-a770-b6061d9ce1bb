#!/usr/bin/env python3
"""
Debug script para investigar por que Koga não recebeu um lendário
"""

def LoadProjectPokemonDatabase():
    """Carrega dados dos Pokémon do projeto"""
    import os
    
    pokemon_data = {}
    
    # Ler Base_Stats.c
    base_stats_file = "src/Base_Stats.c"
    if not os.path.exists(base_stats_file):
        print(f"❌ Arquivo {base_stats_file} não encontrado")
        return {}
    
    try:
        with open(base_stats_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Procurar por definições de Pokémon
        import re
        
        # Padrão para encontrar definições de Pokémon
        pokemon_pattern = r'\[SPECIES_(\w+)\]\s*=\s*\{[^}]*\.baseHP\s*=\s*(\d+)[^}]*\.baseAttack\s*=\s*(\d+)[^}]*\.baseDefense\s*=\s*(\d+)[^}]*\.baseSpeed\s*=\s*(\d+)[^}]*\.baseSpAttack\s*=\s*(\d+)[^}]*\.baseSpDefense\s*=\s*(\d+)[^}]*\.type1\s*=\s*TYPE_(\w+)[^}]*\.type2\s*=\s*TYPE_(\w+)[^}]*\}'
        
        matches = re.finditer(pokemon_pattern, content, re.MULTILINE | re.DOTALL)
        
        # Mapeamento de tipos
        type_mapping = {
            'NORMAL': 0x00, 'FIGHTING': 0x01, 'FLYING': 0x02, 'POISON': 0x03,
            'GROUND': 0x04, 'ROCK': 0x05, 'BUG': 0x06, 'GHOST': 0x07,
            'STEEL': 0x08, 'FIRE': 0x0a, 'WATER': 0x0b, 'GRASS': 0x0c,
            'ELECTRIC': 0x0d, 'PSYCHIC': 0x0e, 'ICE': 0x0f, 'DRAGON': 0x10,
            'DARK': 0x11, 'FAIRY': 0x17
        }
        
        species_counter = 1
        for match in matches:
            name = match.group(1)
            hp = int(match.group(2))
            attack = int(match.group(3))
            defense = int(match.group(4))
            speed = int(match.group(5))
            sp_attack = int(match.group(6))
            sp_defense = int(match.group(7))
            type1_name = match.group(8)
            type2_name = match.group(9)
            
            type1 = type_mapping.get(type1_name, 0x00)
            type2 = type_mapping.get(type2_name, type1)
            
            bst = hp + attack + defense + speed + sp_attack + sp_defense
            
            pokemon_data[species_counter] = {
                'name': name,
                'hp': hp,
                'attack': attack,
                'defense': defense,
                'speed': speed,
                'sp_attack': sp_attack,
                'sp_defense': sp_defense,
                'type1': type1,
                'type2': type2,
                'bst': bst
            }
            
            species_counter += 1
            
            # Limitar para evitar problemas de memória
            if species_counter > 1500:
                break
        
        print(f"✅ Carregados {len(pokemon_data)} Pokémon do projeto")
        return pokemon_data
        
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return {}

def GetLegendaryPokemonWithType(primary_type, secondary_type, party_types):
    """Get legendary Pokemon that match the given types"""

    # Complete legendary Pokemon list from user's memory
    legendary_pokemon = {
        # Gen I Legendaries
        144, 145, 146, 150, 151,  # Articuno, Zapdos, Moltres, Mewtwo, Mew

        # Gen II Legendaries
        243, 244, 245, 249, 250, 251,  # Raikou, Entei, Suicune, Lugia, Ho-Oh, Celebi

        # Gen III Legendaries
        377, 378, 379, 380, 381, 382, 383, 384, 385, 386,  # Regirock, Regice, Registeel, Latias, Latios, Kyogre, Groudon, Rayquaza, Jirachi, Deoxys

        # Gen IV Legendaries
        480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493,  # Lake trio, Dialga, Palkia, Heatran, Regigigas, Giratina, Cresselia, Phione, Manaphy, Darkrai, Shaymin, Arceus

        # Gen V Legendaries
        494, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649,  # Victini, Cobalion, Terrakion, Virizion, Tornadus, Thundurus, Reshiram, Zekrom, Landorus, Kyurem, Keldeo, Meloetta, Genesect

        # Gen VI Legendaries
        716, 717, 718, 719, 720, 721,  # Xerneas, Yveltal, Zygarde, Diancie, Hoopa, Volcanion

        # Gen VII Legendaries
        785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 807, 808, 809,  # Tapu quartet, Cosmog line, Necrozma, Magearna, Marshadow, Zeraora

        # Gen VIII Legendaries
        888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898,  # Zacian, Zamazenta, Eternatus, Kubfu, Urshifu, Regieleki, Regidrago, Glastrier, Spectrier, Calyrex

        # Gen IX Legendaries
        1007, 1008, 1009, 1010  # Koraidon, Miraidon, Walking Wake, Iron Leaves
    }

    # Load project Pokemon database for type checking
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        return []

    matching_legendaries = []

    for legendary_id in legendary_pokemon:
        if legendary_id in project_pokemon_data:
            pokemon_data = project_pokemon_data[legendary_id]
            type1 = pokemon_data.get('type1')
            type2 = pokemon_data.get('type2')

            # Check for type match
            if (type1 == primary_type or type2 == primary_type or
                (secondary_type is not None and (type1 == secondary_type or type2 == secondary_type)) or
                type1 in party_types or type2 in party_types):
                matching_legendaries.append(legendary_id)

    return matching_legendaries

def debug_koga_legendary():
    """Debug específico para Koga"""
    
    print("🔍 DEBUG: Por que Koga não recebeu lendário?")
    print("=" * 60)
    print()
    
    # Dados do Koga
    koga_data = {
        'trainer_id': 418,
        'trainer_name': 'KOGA',
        'category': 'BOSS',
        'primary_type': 0x03,  # POISON
        'secondary_type': 0x07,  # GHOST (do Weezing)
        'party_types': {0x03, 0x07}  # POISON, GHOST
    }
    
    print(f"👤 KOGA:")
    print(f"   ID: {koga_data['trainer_id']}")
    print(f"   Categoria: {koga_data['category']}")
    print(f"   Tipo primário: {koga_data['primary_type']} (POISON)")
    print(f"   Tipo secundário: {koga_data['secondary_type']} (GHOST)")
    print(f"   Tipos da party: {koga_data['party_types']}")
    print()
    
    # Verificar configurações
    print("⚙️ VERIFICANDO CONFIGURAÇÕES:")
    print("-" * 40)
    
    config_file = "include/wild_encounters_config.h"
    try:
        with open(config_file, 'r') as f:
            content = f.read()
        
        max_legendaries_boss = "MAX_LEGENDARIES_PER_BOSS 1" in content
        allow_legendaries_boss = "ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_BOSS TRUE" in content
        
        print(f"   MAX_LEGENDARIES_PER_BOSS 1: {'✅' if max_legendaries_boss else '❌'}")
        print(f"   ALLOW_LEGENDARIES_IN_ADDITIONAL_SLOTS_BOSS TRUE: {'✅' if allow_legendaries_boss else '❌'}")
        
    except Exception as e:
        print(f"   ❌ Erro ao ler configuração: {e}")
    
    print()
    
    # Verificar lendários Poison-type
    print("🏆 VERIFICANDO LENDÁRIOS POISON-TYPE:")
    print("-" * 40)
    
    primary_type = koga_data['primary_type']
    secondary_type = koga_data['secondary_type']
    party_types = koga_data['party_types']
    
    matching_legendaries = GetLegendaryPokemonWithType(primary_type, secondary_type, party_types)
    
    print(f"   Lendários encontrados: {len(matching_legendaries)}")
    
    if matching_legendaries:
        print(f"   IDs: {matching_legendaries}")
        
        # Carregar dados para mostrar detalhes
        project_pokemon_data = LoadProjectPokemonDatabase()
        
        print("\n   📋 DETALHES DOS LENDÁRIOS ENCONTRADOS:")
        for legendary_id in matching_legendaries[:10]:  # Mostrar apenas os primeiros 10
            if legendary_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[legendary_id]
                name = pokemon_data.get('name', 'Unknown')
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                bst = pokemon_data.get('bst', 0)
                
                type1_name = get_type_name(type1)
                type2_name = get_type_name(type2) if type2 != type1 else None
                
                type_str = f"{type1_name}/{type2_name}" if type2_name else type1_name
                
                print(f"     #{legendary_id}: {name} ({type_str}) BST={bst}")
    else:
        print("   ❌ NENHUM lendário Poison/Ghost encontrado!")
        print()
        print("   🔍 INVESTIGANDO POSSÍVEIS CAUSAS:")
        
        # Verificar se existem lendários Poison no projeto
        project_pokemon_data = LoadProjectPokemonDatabase()
        poison_legendaries = []
        
        legendary_pokemon = {144, 145, 146, 150, 151, 243, 244, 245, 249, 250, 251, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386}
        
        for legendary_id in legendary_pokemon:
            if legendary_id in project_pokemon_data:
                pokemon_data = project_pokemon_data[legendary_id]
                type1 = pokemon_data.get('type1')
                type2 = pokemon_data.get('type2')
                
                if type1 == 0x03 or type2 == 0x03:  # POISON
                    poison_legendaries.append(legendary_id)
        
        if poison_legendaries:
            print(f"   ✅ Encontrados {len(poison_legendaries)} lendários Poison no projeto: {poison_legendaries}")
        else:
            print(f"   ❌ NENHUM lendário Poison encontrado no projeto!")
            print(f"   💡 Isso explica por que Koga não recebeu lendário")
    
    print()
    
    # Verificar se a função foi chamada
    print("🔧 POSSÍVEIS CAUSAS:")
    print("-" * 40)
    print("1. ❓ Função ApplyLegendarySubstitution não foi chamada")
    print("2. ❓ Nenhum lendário Poison-type disponível no projeto")
    print("3. ❓ Configuração bloqueando lendários")
    print("4. ❓ Erro na lógica de matching de tipos")
    
    return matching_legendaries

def get_type_name(type_id):
    """Converte ID do tipo para nome"""
    type_names = {
        0x00: 'NORMAL', 0x01: 'FIGHTING', 0x02: 'FLYING', 0x03: 'POISON',
        0x04: 'GROUND', 0x05: 'ROCK', 0x06: 'BUG', 0x07: 'GHOST',
        0x08: 'STEEL', 0x0a: 'FIRE', 0x0b: 'WATER', 0x0c: 'GRASS',
        0x0d: 'ELECTRIC', 0x0e: 'PSYCHIC', 0x0f: 'ICE', 0x10: 'DRAGON',
        0x11: 'DARK', 0x17: 'FAIRY'
    }
    return type_names.get(type_id, f'Unknown({type_id})')

if __name__ == "__main__":
    debug_koga_legendary()
