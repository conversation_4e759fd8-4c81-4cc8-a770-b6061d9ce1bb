# Trainer Randomization Workflow Analysis

## Critical Issues Identified

### 1. Multiple Randomization Passes Problem
**Location**: `scripts/insert.py` lines 1862-1877 and 1927-1942

The system performs **two separate randomization operations** on the same trainer:
1. **Party Expansion Phase** (`ExpandTrainerPartyWithCachedData`) - Lines 2422-2521
2. **Randomization Phase** (`RandomizeTrainerWithCachedData`) - Lines 1907-1945

**The Problem**: Both phases can modify Pokemon, causing the second phase to overwrite the first phase's selections.

### 2. Configuration Inconsistency
**Location**: `scripts/insert.py` lines 5629 and 1947

The `RANDOMIZE_ONLY_ADDITIONAL_POKEMON` configuration is checked in **two different places**:
- **Phase 1** (Expansion): `SelectAdditionalPokemonWithAppropriateTypes()` - Line 5629
- **Phase 2** (Randomization): `RandomizeOnlyAdditionalPokemonWithCache()` - Line 1947

**The Problem**: Different logic paths can lead to inconsistent behavior.

### 3. Party Size Calculation Error
**Location**: `scripts/insert.py` lines 1957-1962

In `RandomizeOnlyAdditionalPokemonWithCache()`:
```python
additional_slots = current_party_size - original_party_size
```

**The Problem**: If party expansion already occurred, `current_party_size` reflects the expanded party, but the function may still attempt to modify original slots.

### 4. Koga Specific Issue
**Location**: Party size limit logic

For Koga (4 original Pokemon + 3 configured additional = 7 total, but limited to 6):
- System should add only 2 additional Pokemon (4 + 2 = 6 max)
- **BUG**: System incorrectly randomizes 1 original Pokemon when hitting the 6-Pokemon limit

## Root Cause Analysis

### Current Problematic Workflow:
```
Step 1: Read original trainer data from ROM
Step 2: Party Expansion (if enabled)
├── SelectAdditionalPokemonWithAppropriateTypes()
├── ├── If RANDOMIZE_ONLY_ADDITIONAL = TRUE: SelectAdditionalPokemonWithoutRandomization()
├── ├── If RANDOMIZE_ONLY_ADDITIONAL = FALSE: SelectAdditionalPokemonWithRandomization()
├── └── BUG: May select real Pokemon instead of placeholders
Step 3: Randomization
├── RandomizeTrainerWithCachedData()
├── ├── If RANDOMIZE_ONLY_ADDITIONAL = TRUE: RandomizeOnlyAdditionalPokemonWithCache()
├── ├── If RANDOMIZE_ONLY_ADDITIONAL = FALSE: RandomizeAllTrainerPokemonWithCache()
├── └── BUG: May re-randomize Pokemon already selected in Step 2
```

### Specific Issues:

#### Issue A: Double Randomization
1. **Expansion Phase**: Selects Pokemon for additional slots
2. **Randomization Phase**: Re-randomizes the same slots, overwriting previous selections

#### Issue B: Original Party Modification
When `RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE`:
- Original party members should NEVER be modified
- **BUG**: System sometimes modifies original members due to incorrect slot indexing

#### Issue C: Type Mismatch
1. **Expansion Phase**: Correctly selects type-matching Pokemon
2. **Randomization Phase**: Replaces them with type-mismatched Pokemon

## Proposed Solution

### Fixed Workflow:
```
Step 1: Read original trainer data from ROM (ONCE)
Step 2: Party Expansion (if enabled)
├── Add placeholder Pokemon to additional slots
├── NEVER randomize - only add placeholders with correct levels
├── Respect party size limits (max 6 Pokemon)
Step 3: Randomization (if enabled)
├── If RANDOMIZE_ONLY_ADDITIONAL = TRUE:
│   ├── NEVER touch original party members (slots 0 to original_party_size-1)
│   ├── ONLY randomize additional slots (slots original_party_size to current_party_size-1)
│   └── Replace placeholders with properly randomized Pokemon
├── If RANDOMIZE_ONLY_ADDITIONAL = FALSE:
│   └── Randomize all Pokemon in party
```

### Key Fixes Required:

#### Fix 1: Separate Expansion and Randomization
- **Expansion Phase**: Only add placeholder Pokemon, never randomize
- **Randomization Phase**: Only randomize placeholders, never touch originals when configured

#### Fix 2: Correct Slot Indexing
- Track original party size separately from current party size
- Use original party size as boundary for "original vs additional" slots

#### Fix 3: Configuration Enforcement
- Check `RANDOMIZE_ONLY_ADDITIONAL_POKEMON` only once in randomization phase
- Remove randomization logic from expansion phase

#### Fix 4: Party Size Limit Handling
- Calculate actual additional slots: `min(configured_additional, max_party_size - original_party_size)`
- Never exceed 6 Pokemon total
- Never modify original Pokemon when hitting limits

## Implementation Plan

### Phase 1: Fix Expansion Logic
**Files to modify**: `scripts/insert.py`

1. **Fix `SelectAdditionalPokemonWithoutRandomization()`** (lines 5656-5743)
   - Remove all randomization logic
   - Only add placeholder Pokemon with correct levels and types
   - Use type-appropriate placeholders (e.g., Koffing for Poison types)

2. **Fix `ExpandTrainerPartyWithCachedData()`** (lines 2422-2521)
   - Ensure party size limits are calculated correctly
   - Never exceed 6 Pokemon total
   - Track original vs additional slots properly

### Phase 2: Fix Randomization Logic
**Files to modify**: `scripts/insert.py`

1. **Fix `RandomizeOnlyAdditionalPokemonWithCache()`** (lines 1947-2100)
   - Use `len(original_party)` as the boundary, not `current_party_size - original_party_size`
   - Add strict validation: `if slot_index < len(original_party): continue`
   - Only process slots from `len(original_party)` to `current_party_size-1`

2. **Fix slot indexing logic**
   - Ensure additional slots start at index `len(original_party)`
   - Never modify slots 0 to `len(original_party)-1` when `RANDOMIZE_ONLY_ADDITIONAL = TRUE`

### Phase 3: Integration Testing
**Files to create**: Test scripts

1. **Create Koga-specific test** (4 original + 2 additional = 6 total)
2. **Create general trainer test** for various party sizes
3. **Verify configuration enforcement** across all trainer categories

## Expected Results

### For Koga with RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE:
- **Original Pokemon** (slots 1-4): Koffing, Muk, Koffing, Weezing - **UNCHANGED**
- **Additional Pokemon** (slots 5-6): Two Poison-type Pokemon - **RANDOMIZED**
- **Total**: 6 Pokemon (4 original + 2 additional)

### For Any Trainer with RANDOMIZE_ONLY_ADDITIONAL_POKEMON = TRUE:
- Original party members: **NEVER MODIFIED**
- Additional party members: **PROPERLY RANDOMIZED**
- Type themes: **PRESERVED**
- Party size limits: **RESPECTED**

## Specific Code Fixes Required

### Fix 1: SelectAdditionalPokemonWithoutRandomization() - Line 5656
**Current Problem**: Function still performs randomization despite name suggesting otherwise

**Required Fix**:
```python
def SelectAdditionalPokemonWithoutRandomization(rom, party_analysis, trainer_class, trainer_id,
                                               trainer_category, max_additional, base_level, pokemon_stats_offset):
    """
    CRITICAL FIX: Returns placeholder Pokemon with correct types but WITHOUT randomization
    These will be properly randomized later in RandomizeOnlyAdditionalPokemonWithCache()
    """
    selected_pokemon = []
    primary_type = party_analysis.get('primary_type')

    # Type-appropriate placeholders (will be randomized later)
    type_placeholders = {
        0x03: 109,  # POISON -> Koffing
        0x0a: 4,    # FIRE -> Charmander
        0x0b: 7,    # WATER -> Squirtle
        0x0c: 1,    # GRASS -> Bulbasaur
        # Add more type mappings as needed
    }

    placeholder_species = type_placeholders.get(primary_type, 25)  # Default: Pikachu

    for i in range(max_additional):
        selected_pokemon.append(placeholder_species)

    return selected_pokemon
```

### Fix 2: RandomizeOnlyAdditionalPokemonWithCache() - Line 1957
**Current Problem**: Incorrect boundary calculation allows modification of original Pokemon

**Required Fix**:
```python
def RandomizeOnlyAdditionalPokemonWithCache(rom, party_offset, current_party_size, original_party,
                                           party_type_analysis, pokemon_stats_offset, trainer_class, trainer_id):
    """
    CRITICAL FIX: Use original_party length as boundary, never modify original slots
    """
    try:
        modifications = 0
        original_party_size = len(original_party)  # CRITICAL: Use this as boundary

        # STRICT VALIDATION: Only process additional slots
        if current_party_size <= original_party_size:
            return 0  # No additional slots to randomize

        # Process ONLY additional slots (from original_party_size to current_party_size-1)
        for slot_index in range(original_party_size, current_party_size):
            # CRITICAL PROTECTION: Never modify original slots
            if slot_index < original_party_size:
                continue  # Skip original Pokemon slots

            # Randomize this additional slot
            pokemon_offset = party_offset + (slot_index * 8)
            # ... rest of randomization logic
```

### Fix 3: Party Size Limit Calculation - Line 2467
**Current Problem**: Doesn't properly handle party size limits causing original Pokemon modification

**Required Fix**:
```python
# Calculate actual additional Pokemon (respecting 6-Pokemon limit)
max_party_size = 6
actual_additional = min(max_additional, max_party_size - current_party_size)

# CRITICAL: Never exceed party size limits
if actual_additional <= 0:
    return 0  # No room for additional Pokemon
```

## Testing Strategy

### Test Case 1: Koga (4 original + 3 configured = 6 actual)
- **Input**: 4 original Pokemon, 3 configured additional
- **Expected**: 4 original unchanged + 2 additional randomized (limited by 6-Pokemon max)
- **Validation**: Original slots 0-3 never modified, slots 4-5 properly randomized

### Test Case 2: Regular Trainer (3 original + 1 configured = 4 actual)
- **Input**: 3 original Pokemon, 1 configured additional
- **Expected**: 3 original unchanged + 1 additional randomized
- **Validation**: Original slots 0-2 never modified, slot 3 properly randomized

### Test Case 3: Full Party (6 original + any configured = 6 actual)
- **Input**: 6 original Pokemon, any additional configured
- **Expected**: 6 original unchanged, no additional Pokemon added
- **Validation**: All slots 0-5 never modified, no expansion occurs
