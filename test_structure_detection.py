#!/usr/bin/env python3
"""
Test script to verify the Pokemon data structure detection fix
Tests the corrected ReadOriginalTrainerPartyData function
"""

import os
import sys
import struct

# Add the scripts directory to the path
sys.path.append('scripts')

def test_koga_party_reading():
    """Test reading <PERSON><PERSON>'s party data with the corrected structure detection"""
    
    print("🧪 TESTING POKEMON DATA STRUCTURE DETECTION")
    print("=" * 60)
    
    # Import the corrected function
    try:
        from insert import ReadOriginalTrainerPartyData, FindTrainerTable
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Check if original ROM exists
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    try:
        with open("BPRE0.gba", "rb") as rom:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            # Test Koga (trainer ID 0x1A2 = 418)
            koga_trainer_id = 0x1A2
            koga_offset = trainer_table_offset + (koga_trainer_id * 40)
            
            print(f"🎯 Testing Koga (Trainer ID {koga_trainer_id})")
            print(f"   Trainer offset: 0x{koga_offset:08X}")
            
            # Read trainer header
            rom.seek(koga_offset)
            trainer_data = rom.read(40)
            
            if len(trainer_data) < 40:
                print("❌ Failed to read trainer data")
                return False
            
            party_flags = trainer_data[0]
            trainer_class = trainer_data[1]
            party_size = trainer_data[32]
            party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
            
            print(f"   Party flags: 0x{party_flags:02X}")
            print(f"   Trainer class: {trainer_class}")
            print(f"   Party size: {party_size}")
            print(f"   Party pointer: 0x{party_ptr:08X}")
            
            # Validate pointer
            if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                print("❌ Invalid party pointer")
                return False
            
            party_offset = party_ptr - 0x08000000
            print(f"   Party offset: 0x{party_offset:08X}")
            
            # Test the corrected function
            print("\n🔍 Testing structure detection...")
            
            # Test without party_flags (auto-detection)
            print("   Testing auto-detection...")
            party_data_auto = ReadOriginalTrainerPartyData(rom, party_offset, party_size)
            
            # Test with party_flags
            print("   Testing with party flags...")
            party_data_flags = ReadOriginalTrainerPartyData(rom, party_offset, party_size, party_flags)
            
            # Compare results
            print("\n📊 RESULTS:")
            print("-" * 40)
            
            if party_data_auto:
                print(f"✅ Auto-detection successful: {len(party_data_auto)} Pokemon found")
                for i, pokemon in enumerate(party_data_auto):
                    structure = pokemon.get('structure', 'unknown')
                    print(f"   Slot {i+1}: Species {pokemon['species']} Lv.{pokemon['level']} ({structure})")
            else:
                print("❌ Auto-detection failed")
            
            print()
            
            if party_data_flags:
                print(f"✅ Flags-based detection successful: {len(party_data_flags)} Pokemon found")
                for i, pokemon in enumerate(party_data_flags):
                    structure = pokemon.get('structure', 'unknown')
                    print(f"   Slot {i+1}: Species {pokemon['species']} Lv.{pokemon['level']} ({structure})")
            else:
                print("❌ Flags-based detection failed")
            
            # Verify expected Koga party
            expected_koga = [
                {'species': 109, 'level': 37},  # Koffing
                {'species': 89, 'level': 39},   # Muk
                {'species': 109, 'level': 37},  # Koffing
                {'species': 110, 'level': 43}   # Weezing
            ]
            
            print(f"\n🎯 Expected Koga party:")
            for i, pokemon in enumerate(expected_koga):
                print(f"   Slot {i+1}: Species {pokemon['species']} Lv.{pokemon['level']}")
            
            # Check if results match expected
            success = False
            if party_data_flags and len(party_data_flags) == 4:
                matches = 0
                for i, pokemon in enumerate(party_data_flags):
                    if (pokemon['species'] == expected_koga[i]['species'] and 
                        pokemon['level'] == expected_koga[i]['level']):
                        matches += 1
                
                if matches == 4:
                    print("\n✅ PERFECT MATCH! Structure detection is working correctly!")
                    success = True
                elif matches >= 2:
                    print(f"\n⚠️  PARTIAL MATCH: {matches}/4 Pokemon match expected values")
                else:
                    print(f"\n❌ POOR MATCH: Only {matches}/4 Pokemon match expected values")
            
            if not success and party_data_auto:
                print("\n🔄 Checking auto-detection results...")
                if len(party_data_auto) == 4:
                    matches = 0
                    for i, pokemon in enumerate(party_data_auto):
                        if (pokemon['species'] == expected_koga[i]['species'] and 
                            pokemon['level'] == expected_koga[i]['level']):
                            matches += 1
                    
                    if matches == 4:
                        print("✅ AUTO-DETECTION PERFECT MATCH!")
                        success = True
                    elif matches >= 2:
                        print(f"⚠️  AUTO-DETECTION PARTIAL MATCH: {matches}/4")
                    else:
                        print(f"❌ AUTO-DETECTION POOR MATCH: {matches}/4")
            
            return success
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_trainers():
    """Test structure detection on multiple trainers"""
    
    print("\n🧪 TESTING MULTIPLE TRAINERS")
    print("=" * 60)
    
    try:
        from insert import ReadOriginalTrainerPartyData, FindTrainerTable
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    if not os.path.exists("BPRE0.gba"):
        print("❌ Original ROM 'BPRE0.gba' not found")
        return False
    
    # Test trainers with known data
    test_trainers = [
        {'id': 0x19E, 'name': 'BROCK'},
        {'id': 0x19F, 'name': 'MISTY'},
        {'id': 0x1A0, 'name': 'LT_SURGE'},
        {'id': 0x1A1, 'name': 'ERIKA'},
        {'id': 0x1A2, 'name': 'KOGA'},
    ]
    
    try:
        with open("BPRE0.gba", "rb") as rom:
            trainer_table_offset = FindTrainerTable(rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            success_count = 0
            total_count = 0
            
            for trainer in test_trainers:
                trainer_id = trainer['id']
                trainer_name = trainer['name']
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                
                print(f"\n🎯 Testing {trainer_name} (ID {trainer_id:03X})")
                
                # Read trainer header
                rom.seek(trainer_offset)
                trainer_data = rom.read(40)
                
                if len(trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                party_flags = trainer_data[0]
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                
                if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                    print(f"   ❌ Invalid party pointer: 0x{party_ptr:08X}")
                    continue
                
                party_offset = party_ptr - 0x08000000
                
                # Test structure detection
                party_data = ReadOriginalTrainerPartyData(rom, party_offset, party_size, party_flags)
                
                total_count += 1
                
                if party_data and len(party_data) > 0:
                    print(f"   ✅ Success: {len(party_data)} Pokemon found")
                    
                    # Check for reasonable data
                    valid_pokemon = 0
                    for pokemon in party_data:
                        if (1 <= pokemon['level'] <= 100 and 
                            1 <= pokemon['species'] <= 1440):
                            valid_pokemon += 1
                    
                    if valid_pokemon == len(party_data):
                        print(f"   ✅ All Pokemon data looks valid")
                        success_count += 1
                    else:
                        print(f"   ⚠️  {valid_pokemon}/{len(party_data)} Pokemon have valid data")
                else:
                    print(f"   ❌ Failed to read party data")
            
            print(f"\n📊 SUMMARY: {success_count}/{total_count} trainers read successfully")
            return success_count == total_count
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 POKEMON DATA STRUCTURE DETECTION TEST")
    print("=" * 60)
    
    # Test Koga specifically
    koga_success = test_koga_party_reading()
    
    # Test multiple trainers
    multi_success = test_multiple_trainers()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS:")
    print(f"   Koga test: {'✅ PASS' if koga_success else '❌ FAIL'}")
    print(f"   Multi test: {'✅ PASS' if multi_success else '❌ FAIL'}")
    
    if koga_success and multi_success:
        print("\n🎉 ALL TESTS PASSED! Structure detection is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! Structure detection needs more work.")
        sys.exit(1)
