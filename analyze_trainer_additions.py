#!/usr/bin/env python3
"""
Analyze the Pokemon additions to trainers after compilation to verify type appropriateness
"""

import os
import sys
import struct

# Add the scripts directory to the path
sys.path.append('scripts')

def analyze_gym_leader_parties():
    """Analyze gym leader parties to verify Pokemon additions are type-appropriate"""
    
    print("🔍 ANALYZING GYM LEADER POKEMON ADDITIONS")
    print("=" * 60)
    
    if not os.path.exists("test.gba"):
        print("❌ Compiled ROM 'test.gba' not found")
        return False
    
    try:
        from insert import (
            FindTrainerTable, 
            ReadOriginalTrainerPartyData,
            LoadProjectPokemonDatabase
        )
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Load project Pokemon database for type information
    project_pokemon_data = LoadProjectPokemonDatabase()
    if not project_pokemon_data:
        print("❌ Failed to load project Pokemon database")
        return False
    
    print(f"✅ Loaded {len(project_pokemon_data)} Pokemon from project database")
    
    # Gym leader information with expected types
    gym_leaders = {
        0x19E: {
            'name': 'BROCK',
            'expected_types': ['Rock', 'Ground'],
            'type_ids': [0x05, 0x04]  # TYPE_ROCK, TYPE_GROUND
        },
        0x19F: {
            'name': 'MISTY', 
            'expected_types': ['Water'],
            'type_ids': [0x0b]  # TYPE_WATER
        },
        0x1A0: {
            'name': 'LT_SURGE',
            'expected_types': ['Electric'],
            'type_ids': [0x0d]  # TYPE_ELECTRIC
        },
        0x1A1: {
            'name': 'ERIKA',
            'expected_types': ['Grass'],
            'type_ids': [0x0c]  # TYPE_GRASS
        },
        0x1A2: {
            'name': 'KOGA',
            'expected_types': ['Poison'],
            'type_ids': [0x03]  # TYPE_POISON
        },
        0x1A4: {
            'name': 'SABRINA',
            'expected_types': ['Psychic'],
            'type_ids': [0x0e]  # TYPE_PSYCHIC
        },
        0x1A3: {
            'name': 'BLAINE',
            'expected_types': ['Fire'],
            'type_ids': [0x0a]  # TYPE_FIRE
        },
        0x15E: {
            'name': 'GIOVANNI',
            'expected_types': ['Ground', 'Rock'],
            'type_ids': [0x04, 0x05]  # TYPE_GROUND, TYPE_ROCK
        }
    }
    
    # Type names mapping
    type_names = {
        0x00: 'Normal', 0x01: 'Fighting', 0x02: 'Flying', 0x03: 'Poison',
        0x04: 'Ground', 0x05: 'Rock', 0x06: 'Bug', 0x07: 'Ghost',
        0x08: 'Steel', 0x0a: 'Fire', 0x0b: 'Water', 0x0c: 'Grass',
        0x0d: 'Electric', 0x0e: 'Psychic', 0x0f: 'Ice', 0x10: 'Dragon',
        0x11: 'Dark', 0x17: 'Fairy'
    }
    
    try:
        with open("test.gba", "rb") as rom, open("BPRE0.gba", "rb") as original_rom:
            # Find trainer table
            trainer_table_offset = FindTrainerTable(rom)
            if trainer_table_offset == 0:
                print("❌ Trainer table not found")
                return False
            
            print(f"✅ Trainer table found at: 0x{trainer_table_offset:08X}")
            
            analysis_results = {}
            
            for trainer_id, leader_info in gym_leaders.items():
                leader_name = leader_info['name']
                expected_types = leader_info['expected_types']
                expected_type_ids = leader_info['type_ids']
                
                print(f"\n🎯 Analyzing {leader_name}")
                print(f"   Expected types: {', '.join(expected_types)}")
                
                # Read trainer header
                trainer_offset = trainer_table_offset + (trainer_id * 40)
                rom.seek(trainer_offset)
                trainer_data = rom.read(40)
                
                if len(trainer_data) < 40:
                    print(f"   ❌ Failed to read trainer data")
                    continue
                
                party_flags = trainer_data[0]
                party_size = trainer_data[32]
                party_ptr = struct.unpack('<I', trainer_data[36:40])[0]
                
                if party_ptr < 0x08000000 or party_ptr > 0x09FFFFFF:
                    print(f"   ❌ Invalid party pointer: 0x{party_ptr:08X}")
                    continue
                
                party_offset = party_ptr - 0x08000000
                
                print(f"   Party size: {party_size}")
                
                # Read original party for comparison
                original_party = ReadOriginalTrainerPartyData(original_rom, party_offset, party_size, party_flags)
                if not original_party:
                    print(f"   ❌ Failed to read original party")
                    continue
                
                original_party_size = len(original_party)
                print(f"   Original party size: {original_party_size}")
                
                # Read current party (after expansion and randomization)
                current_party = ReadOriginalTrainerPartyData(rom, party_offset, party_size, party_flags)
                if not current_party:
                    print(f"   ❌ Failed to read current party")
                    continue
                
                print(f"   Current party size: {len(current_party)}")
                
                # Analyze original Pokemon types
                print(f"   📋 Original Pokemon:")
                original_types = set()
                for i, pokemon in enumerate(original_party):
                    species_id = pokemon['species']
                    level = pokemon['level']
                    
                    if species_id in project_pokemon_data:
                        pokemon_data = project_pokemon_data[species_id]
                        type1 = pokemon_data.get('type1')
                        type2 = pokemon_data.get('type2')
                        
                        type1_name = type_names.get(type1, f'Unknown({type1})')
                        type2_name = type_names.get(type2, f'Unknown({type2})') if type2 != type1 else None
                        
                        type_str = type1_name
                        if type2_name:
                            type_str += f"/{type2_name}"
                        
                        print(f"      Slot {i+1}: #{species_id} Lv.{level} ({type_str})")
                        
                        original_types.add(type1)
                        if type2 and type2 != type1:
                            original_types.add(type2)
                
                # Analyze additional Pokemon
                if len(current_party) > original_party_size:
                    print(f"   ➕ Additional Pokemon:")
                    
                    type_appropriate = 0
                    total_additional = 0
                    legendary_count = 0
                    
                    for i in range(original_party_size, len(current_party)):
                        pokemon = current_party[i]
                        species_id = pokemon['species']
                        level = pokemon['level']
                        total_additional += 1
                        
                        if species_id in project_pokemon_data:
                            pokemon_data = project_pokemon_data[species_id]
                            type1 = pokemon_data.get('type1')
                            type2 = pokemon_data.get('type2')
                            bst = pokemon_data.get('bst', 0)
                            
                            type1_name = type_names.get(type1, f'Unknown({type1})')
                            type2_name = type_names.get(type2, f'Unknown({type2})') if type2 != type1 else None
                            
                            type_str = type1_name
                            if type2_name:
                                type_str += f"/{type2_name}"
                            
                            # Check if legendary
                            from insert import IsTrainerPokemonLegendary
                            is_legendary = IsTrainerPokemonLegendary(species_id)
                            legendary_mark = " ⭐" if is_legendary else ""
                            if is_legendary:
                                legendary_count += 1
                            
                            print(f"      Slot {i+1}: #{species_id} Lv.{level} ({type_str}) BST:{bst}{legendary_mark}")
                            
                            # Check type appropriateness
                            type_match = False
                            if type1 in expected_type_ids or type1 in original_types:
                                type_match = True
                            if type2 and (type2 in expected_type_ids or type2 in original_types):
                                type_match = True
                            
                            if type_match:
                                type_appropriate += 1
                                print(f"         ✅ Type appropriate")
                            else:
                                print(f"         ⚠️  Type mismatch (expected: {', '.join(expected_types)})")
                    
                    # Summary for this gym leader
                    appropriateness_rate = (type_appropriate / total_additional * 100) if total_additional > 0 else 0
                    
                    print(f"   📊 Summary:")
                    print(f"      Additional Pokemon: {total_additional}")
                    print(f"      Type appropriate: {type_appropriate}/{total_additional} ({appropriateness_rate:.1f}%)")
                    print(f"      Legendary Pokemon: {legendary_count}")
                    
                    analysis_results[leader_name] = {
                        'total_additional': total_additional,
                        'type_appropriate': type_appropriate,
                        'appropriateness_rate': appropriateness_rate,
                        'legendary_count': legendary_count
                    }
                else:
                    print(f"   ℹ️  No additional Pokemon found")
                    analysis_results[leader_name] = {
                        'total_additional': 0,
                        'type_appropriate': 0,
                        'appropriateness_rate': 0,
                        'legendary_count': 0
                    }
            
            # Overall summary
            print(f"\n📊 OVERALL ANALYSIS SUMMARY")
            print("=" * 60)
            
            total_additional_all = sum(r['total_additional'] for r in analysis_results.values())
            total_appropriate_all = sum(r['type_appropriate'] for r in analysis_results.values())
            total_legendary_all = sum(r['legendary_count'] for r in analysis_results.values())
            
            overall_rate = (total_appropriate_all / total_additional_all * 100) if total_additional_all > 0 else 0
            
            print(f"Total additional Pokemon: {total_additional_all}")
            print(f"Type appropriate: {total_appropriate_all}/{total_additional_all} ({overall_rate:.1f}%)")
            print(f"Total legendary Pokemon: {total_legendary_all}")
            
            # Individual leader summary
            print(f"\n📋 Individual Leader Summary:")
            for leader_name, results in analysis_results.items():
                rate = results['appropriateness_rate']
                status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
                print(f"   {status} {leader_name}: {results['type_appropriate']}/{results['total_additional']} ({rate:.1f}%) + {results['legendary_count']} legendary")
            
            return overall_rate >= 70  # Consider success if 70%+ are type appropriate
            
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 TRAINER POKEMON ADDITION ANALYSIS")
    print("=" * 60)
    
    success = analyze_gym_leader_parties()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ANALYSIS COMPLETE: Pokemon additions are generally type-appropriate!")
    else:
        print("⚠️ ANALYSIS COMPLETE: Some Pokemon additions may not be type-appropriate.")
    
    sys.exit(0 if success else 1)
